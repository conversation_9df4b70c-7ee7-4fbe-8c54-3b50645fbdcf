package com.laoshu198838.dto.debt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 债权转换请求 DTO
 * 用于诉讼与非诉讼债权相互转换的请求数据传输
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DebtConversionRequestDTO {
    
    /**
     * 债权人
     */
    @NotBlank(message = "债权人不能为空")
    private String creditor;
    
    /**
     * 债务人
     */
    @NotBlank(message = "债务人不能为空")
    private String debtor;
    
    /**
     * 期间
     */
    @NotBlank(message = "期间不能为空")
    private String period;
    
    /**
     * 年份
     */
    @NotNull(message = "年份不能为空")
    private Integer year;
    
    /**
     * 月份
     */
    @NotNull(message = "月份不能为空")
    private Integer month;
    
    /**
     * 转换年份
     */
    @NotNull(message = "转换年份不能为空")
    private Integer conversionYear;
    
    /**
     * 转换月份
     */
    @NotNull(message = "转换月份不能为空")
    private Integer conversionMonth;
    
    /**
     * 备注
     */
    private String remark;
    
    // === 非诉讼转诉讼特有字段 ===
    
    /**
     * 诉讼案件名称（非诉讼转诉讼时必填）
     */
    private String litigationCase;
    
    /**
     * 诉讼主张本金
     */
    private BigDecimal litigationOccurredPrincipal;
    
    /**
     * 诉讼主张利息及罚金
     */
    private BigDecimal litigationInterestFee;
    
    /**
     * 诉讼费
     */
    private BigDecimal litigationFee;
    
    /**
     * 中介费
     */
    private BigDecimal intermediaryFee;
}