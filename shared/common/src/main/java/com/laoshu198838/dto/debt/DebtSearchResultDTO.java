package com.laoshu198838.dto.debt;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;

/**
 * 债权搜索结果 DTO
 * 用于返回可转换的债权记录信息
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DebtSearchResultDTO {
    
    /**
     * 债权人
     */
    private String creditor;
    
    /**
     * 债务人
     */
    private String debtor;
    
    /**
     * 期间
     */
    private String period;
    
    /**
     * 债权余额
     */
    private BigDecimal debtBalance;
    
    /**
     * 当前状态：诉讼 或 非诉讼
     */
    private String currentStatus;
    
    /**
     * 是否涉诉：是 或 否
     */
    private String isLitigation;
    
    /**
     * 管理公司
     */
    private String managementCompany;
    
    /**
     * 科目名称
     */
    private String subjectName;
    
    /**
     * 年份
     */
    private Integer year;
    
    /**
     * 月份
     */
    private Integer month;
    
    /**
     * 数据来源表：litigation 或 non_litigation
     */
    private String sourceTable;
    
    /**
     * 案件名称（诉讼债权专有）
     */
    private String litigationCase;
    
    /**
     * 本金（非诉讼债权专有）
     */
    private BigDecimal principal;
    
    /**
     * 利息（非诉讼债权专有）
     */
    private BigDecimal interest;
    
    /**
     * 违约金（非诉讼债权专有）
     */
    private BigDecimal penalty;
}