package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 非诉讼表实体类
 * 对应数据库中的非诉讼表
 * <AUTHOR>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "非诉讼表")
public class NonLitigationClaim {

    @EmbeddedId
    private NonLitigationCompositeKey id;

    @Column(name = "序号", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int sequence;

    @Column(name = "科目名称", length = 30)
    private String subjectName;

    @Column(name = "债权到期时间")
    private Date dueDate;

    @Column(name = "2022年4月30日债权账面余额")
    private BigDecimal balance20220430;

    @Column(name = "上月末本金")
    private BigDecimal lastMonthPrincipal;

    @Column(name = "上月末利息")
    private BigDecimal lastMonthInterest;

    @Column(name = "上月末违约金")
    private BigDecimal lastMonthPenalty;

    @Column(name = "本月本金增减")
    private BigDecimal currentMonthPrincipalIncreaseDecrease;

    @Column(name = "本月利息增减")
    private BigDecimal currentMonthInterestIncreaseDecrease;

    @Column(name = "本月违约金增减")
    private BigDecimal currentMonthPenaltyIncreaseDecrease;

    @Column(name = "本月末本金")
    private BigDecimal currentMonthPrincipal;

    @Column(name = "本月末利息")
    private BigDecimal currentMonthInterest;

    @Column(name = "本月末违约金")
    private BigDecimal currentMonthPenalty;

    @Column(name = "下月回收预计")
    private BigDecimal nextMonthRecoveryEstimate;

    @Column(name = "本年度回收目标")
    private BigDecimal annualRecoveryTarget;

    @Column(name = "本年度累计回收")
    private BigDecimal annualCumulativeRecovery;

    @Column(name = "安排措施", length = 190)
    private String arrangement;

    @Column(name = "责任人", length = 30)
    private String responsiblePerson;

    @Column(name = "备注", length = 190)
    private String remark;

    @Column(name = "逾期年限", length = 30)
    private String overdueYear;

    @Column(name = "债权类别", length = 30)
    private String creditorCategory;

    @Column(name = "债权类型", length = 30)
    private String debtType;

    @Column(name = "债权性质", length = 30)
    private String creditorNature;

    @Column(name = "管理公司", length = 30)
    private String managementCompany;

    @Column(name = "本月新增债权")
    private BigDecimal currentMonthNewDebt;

    @Column(name = "本月处置债权")
    private BigDecimal currentMonthDisposedDebt;

    // 本月末余额是计算字段，不在数据库中存储
    @jakarta.persistence.Transient
    private BigDecimal currentMonthBalance;

    /**
     * 计算本月末余额
     * 本月末余额 = 本月末本金 + 本月末利息 + 本月末违约金
     */
    @PrePersist
    @PreUpdate
    public void calculateCurrentMonthBalance() {
        BigDecimal principal = currentMonthPrincipal != null ? currentMonthPrincipal : BigDecimal.ZERO;
        BigDecimal interest = currentMonthInterest != null ? currentMonthInterest : BigDecimal.ZERO;
        BigDecimal penalty = currentMonthPenalty != null ? currentMonthPenalty : BigDecimal.ZERO;
        this.currentMonthBalance = principal.add(interest).add(penalty);
    }

    /**
     * 获取本月末余额
     * 如果currentMonthBalance为null，则重新计算
     */
    public BigDecimal getCurrentMonthBalance() {
        if (currentMonthBalance == null) {
            calculateCurrentMonthBalance();
        }
        return currentMonthBalance;
    }

    /**
     * 非诉讼表专用的复合主键类
     * <p>
     * 该类封装“非诉讼表”的联合主键字段，
     * 包括债权人、债务人、期间、年份和月份等信息。
     * </p>
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @EqualsAndHashCode
    @Embeddable
    public static class NonLitigationCompositeKey implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name = "债权人")
        private String creditor;

        @Column(name = "债务人")
        private String debtor;

        @Column(name = "期间")
        private String period;

        @Column(name = "年份")
        private Integer year;

        @Column(name = "月份")
        private Integer month;

        // 默认无参构造函数
        public NonLitigationCompositeKey() {}

        // 带参数的构造函数，用于创建复合键实例
        public NonLitigationCompositeKey(String creditor, String debtor, String period, Integer year, Integer month) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.year = year;
            this.month = month;
        }

        // 带参数的构造函数，不包含月份
        public NonLitigationCompositeKey(String creditor, String debtor, String period, Integer year) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.year = year;
        }
    }
}
